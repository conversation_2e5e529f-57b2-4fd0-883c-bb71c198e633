[{"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/current-user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.UserLoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "Register", "RelativePath": "api/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.UserRegisterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "RevokeRefreshToken", "RelativePath": "api/revoke-refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "GetById", "RelativePath": "api/user/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "Delete", "RelativePath": "api/user/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "VerifyOtp", "RelativePath": "api/verify-otp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.VerifyOtpRequest", "IsRequired": true}], "ReturnTypes": []}]